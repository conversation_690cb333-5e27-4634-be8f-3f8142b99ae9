<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hi-Lo Card Counting Trainer</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🃏 Hi-Lo Card Counting Trainer</h1>
            <p>Master the Hi-Lo strategy for Blackjack and Poker</p>
        </header>

        <nav class="tab-nav">
            <button class="tab-btn active" data-tab="learn">Learn</button>
            <button class="tab-btn" data-tab="practice">Practice</button>
            <button class="tab-btn" data-tab="drill">Speed Drill</button>
            <button class="tab-btn" data-tab="game">Game Sim</button>
            <button class="tab-btn" data-tab="stats">Statistics</button>
        </nav>

        <!-- Learn Tab -->
        <div id="learn" class="tab-content active">
            <div class="learn-section">
                <h2>Hi-Lo Card Counting Strategy</h2>
                <div class="strategy-explanation">
                    <h3>How Hi-<PERSON> Works</h3>
                    <p>The Hi-Lo system assigns values to cards to track the ratio of high to low cards remaining in the deck:</p>
                    
                    <div class="card-values">
                        <div class="value-group low-cards">
                            <h4>Low Cards (+1)</h4>
                            <div class="cards">2, 3, 4, 5, 6</div>
                            <p>These favor the dealer</p>
                        </div>
                        <div class="value-group neutral-cards">
                            <h4>Neutral Cards (0)</h4>
                            <div class="cards">7, 8, 9</div>
                            <p>No effect on count</p>
                        </div>
                        <div class="value-group high-cards">
                            <h4>High Cards (-1)</h4>
                            <div class="cards">10, J, Q, K, A</div>
                            <p>These favor the player</p>
                        </div>
                    </div>

                    <div class="strategy-tips">
                        <h3>Key Concepts</h3>
                        <ul>
                            <li><strong>Running Count:</strong> The ongoing total as cards are dealt</li>
                            <li><strong>True Count:</strong> Running count ÷ estimated decks remaining</li>
                            <li><strong>Positive Count:</strong> More high cards remaining (player advantage)</li>
                            <li><strong>Negative Count:</strong> More low cards remaining (dealer advantage)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Practice Tab -->
        <div id="practice" class="tab-content">
            <div class="practice-section">
                <h2>Basic Counting Practice</h2>
                <div class="practice-controls">
                    <button id="start-practice" class="btn primary">Start Practice</button>
                    <button id="reset-practice" class="btn secondary">Reset</button>
                    <div class="difficulty">
                        <label>Speed: </label>
                        <select id="practice-speed">
                            <option value="3000">Slow (3s)</option>
                            <option value="2000" selected>Medium (2s)</option>
                            <option value="1000">Fast (1s)</option>
                            <option value="500">Very Fast (0.5s)</option>
                        </select>
                    </div>
                </div>

                <div class="practice-area">
                    <div class="current-card">
                        <div id="practice-card" class="card-display">Click Start to Begin</div>
                    </div>
                    
                    <div class="count-display">
                        <div class="count-section">
                            <label>Your Count:</label>
                            <input type="number" id="user-count" placeholder="0">
                        </div>
                        <div class="count-section">
                            <label>Actual Count:</label>
                            <span id="actual-count">0</span>
                        </div>
                    </div>

                    <div class="practice-stats">
                        <div class="stat">
                            <span class="stat-label">Cards Shown:</span>
                            <span id="cards-shown">0</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Accuracy:</span>
                            <span id="accuracy">100%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Speed Drill Tab -->
        <div id="drill" class="tab-content">
            <div class="drill-section">
                <h2>Speed Drill Challenge</h2>
                <div class="drill-controls">
                    <button id="start-drill" class="btn primary">Start 30-Second Drill</button>
                    <div class="drill-settings">
                        <label>
                            <input type="checkbox" id="show-values" checked>
                            Show card values
                        </label>
                    </div>
                </div>

                <div class="drill-area">
                    <div class="timer">
                        <span id="drill-timer">30</span> seconds
                    </div>
                    
                    <div class="drill-cards">
                        <div id="drill-card" class="card-display large">Ready?</div>
                    </div>

                    <div class="drill-input">
                        <label>Final Count:</label>
                        <input type="number" id="drill-count" placeholder="Enter final count">
                        <button id="submit-drill" class="btn secondary" disabled>Submit</button>
                    </div>

                    <div class="drill-results" id="drill-results" style="display: none;">
                        <h3>Results</h3>
                        <div class="result-stats">
                            <div class="result-stat">
                                <span class="label">Cards Processed:</span>
                                <span id="drill-cards-count">0</span>
                            </div>
                            <div class="result-stat">
                                <span class="label">Your Count:</span>
                                <span id="drill-user-count">0</span>
                            </div>
                            <div class="result-stat">
                                <span class="label">Correct Count:</span>
                                <span id="drill-correct-count">0</span>
                            </div>
                            <div class="result-stat">
                                <span class="label">Accuracy:</span>
                                <span id="drill-accuracy">0%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Simulation Tab -->
        <div id="game" class="tab-content">
            <div class="game-section">
                <h2>Blackjack Simulation</h2>
                <div class="game-controls">
                    <button id="start-game" class="btn primary">Start New Game</button>
                    <select id="deck-count">
                        <option value="1">Single Deck</option>
                        <option value="2">Double Deck</option>
                        <option value="6" selected>6-Deck Shoe</option>
                        <option value="8">8-Deck Shoe</option>
                    </select>
                </div>

                <div class="game-area">
                    <div class="game-info">
                        <div class="info-item">
                            <span class="label">Running Count:</span>
                            <span id="game-running-count">0</span>
                        </div>
                        <div class="info-item">
                            <span class="label">True Count:</span>
                            <span id="game-true-count">0</span>
                        </div>
                        <div class="info-item">
                            <span class="label">Decks Remaining:</span>
                            <span id="game-decks-remaining">6</span>
                        </div>
                    </div>

                    <div class="game-table">
                        <div class="dealer-area">
                            <h3>Dealer</h3>
                            <div id="dealer-cards" class="card-area"></div>
                        </div>
                        
                        <div class="player-area">
                            <h3>Player</h3>
                            <div id="player-cards" class="card-area"></div>
                        </div>
                    </div>

                    <div class="game-actions">
                        <button id="deal-hand" class="btn secondary" disabled>Deal Hand</button>
                        <button id="hit-card" class="btn secondary" disabled>Hit</button>
                        <button id="stand" class="btn secondary" disabled>Stand</button>
                        <button id="next-hand" class="btn secondary" disabled>Next Hand</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Tab -->
        <div id="stats" class="tab-content">
            <div class="stats-section">
                <h2>Your Progress</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Overall Accuracy</h3>
                        <div class="stat-value" id="overall-accuracy">0%</div>
                    </div>
                    <div class="stat-card">
                        <h3>Cards Practiced</h3>
                        <div class="stat-value" id="total-cards">0</div>
                    </div>
                    <div class="stat-card">
                        <h3>Best Speed</h3>
                        <div class="stat-value" id="best-speed">0 cards/min</div>
                    </div>
                    <div class="stat-card">
                        <h3>Practice Sessions</h3>
                        <div class="stat-value" id="total-sessions">0</div>
                    </div>
                </div>

                <div class="progress-chart">
                    <h3>Accuracy Over Time</h3>
                    <canvas id="accuracy-chart" width="400" height="200"></canvas>
                </div>

                <div class="stats-actions">
                    <button id="reset-stats" class="btn danger">Reset All Statistics</button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
