/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #333;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Navigation Tabs */
.tab-nav {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 5px;
    backdrop-filter: blur(10px);
}

.tab-btn {
    background: transparent;
    border: none;
    color: white;
    padding: 12px 24px;
    cursor: pointer;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    flex: 1;
    max-width: 150px;
}

.tab-btn:hover {
    background: rgba(255,255,255,0.2);
}

.tab-btn.active {
    background: white;
    color: #2a5298;
    font-weight: bold;
}

/* Tab Content */
.tab-content {
    display: none;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    min-height: 600px;
}

.tab-content.active {
    display: block;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn.primary {
    background: #4CAF50;
    color: white;
}

.btn.primary:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.btn.secondary {
    background: #2196F3;
    color: white;
}

.btn.secondary:hover {
    background: #1976D2;
    transform: translateY(-2px);
}

.btn.danger {
    background: #f44336;
    color: white;
}

.btn.danger:hover {
    background: #d32f2f;
}

.btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* Learn Tab Styles */
.strategy-explanation h2 {
    color: #2a5298;
    margin-bottom: 20px;
    font-size: 2rem;
}

.strategy-explanation h3 {
    color: #333;
    margin: 20px 0 15px 0;
    font-size: 1.4rem;
}

.card-values {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.value-group {
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.value-group.low-cards {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.value-group.neutral-cards {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.value-group.high-cards {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.value-group h4 {
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.value-group .cards {
    font-size: 1.5rem;
    font-weight: bold;
    margin: 10px 0;
    color: #333;
}

.strategy-tips ul {
    list-style: none;
    padding-left: 0;
}

.strategy-tips li {
    background: #f8f9fa;
    margin: 10px 0;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #2a5298;
}

/* Practice Tab Styles */
.practice-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.difficulty {
    display: flex;
    align-items: center;
    gap: 10px;
}

.difficulty select {
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
}

.practice-area {
    text-align: center;
}

.current-card {
    margin-bottom: 30px;
}

.card-display {
    background: white;
    border: 3px solid #2a5298;
    border-radius: 15px;
    padding: 40px;
    font-size: 3rem;
    font-weight: bold;
    color: #2a5298;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.card-display.large {
    font-size: 4rem;
    min-height: 200px;
}

.card-display.red {
    color: #d32f2f;
    border-color: #d32f2f;
}

.count-display {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.count-section {
    text-align: center;
}

.count-section label {
    display: block;
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.count-section input {
    padding: 12px;
    font-size: 1.5rem;
    text-align: center;
    border: 2px solid #ddd;
    border-radius: 8px;
    width: 100px;
}

.count-section span {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2a5298;
}

.practice-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 30px;
    flex-wrap: wrap;
}

.stat {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    min-width: 120px;
}

.stat-label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
    color: #666;
}

/* Drill Tab Styles */
.drill-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.drill-settings label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.timer {
    text-align: center;
    font-size: 2rem;
    font-weight: bold;
    color: #2a5298;
    margin-bottom: 20px;
}

.drill-input {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.drill-input input {
    padding: 12px;
    font-size: 1.2rem;
    text-align: center;
    border: 2px solid #ddd;
    border-radius: 8px;
    width: 120px;
}

.drill-results {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.result-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.result-stat {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background: white;
    border-radius: 6px;
}

/* Game Tab Styles */
.game-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.game-controls select {
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
}

.game-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.info-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    min-width: 140px;
}

.info-item .label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
    color: #666;
}

.game-table {
    margin: 30px 0;
}

.dealer-area, .player-area {
    margin: 20px 0;
    text-align: center;
}

.dealer-area h3, .player-area h3 {
    margin-bottom: 15px;
    color: #2a5298;
}

.card-area {
    min-height: 100px;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.game-card {
    background: white;
    border: 2px solid #333;
    border-radius: 8px;
    padding: 15px 10px;
    font-size: 1.2rem;
    font-weight: bold;
    min-width: 50px;
    text-align: center;
}

.game-card.red {
    color: #d32f2f;
}

.game-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* Stats Tab Styles */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card h3 {
    margin-bottom: 15px;
    font-size: 1.1rem;
    opacity: 0.9;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: bold;
}

.progress-chart {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    text-align: center;
}

.progress-chart h3 {
    margin-bottom: 20px;
    color: #2a5298;
}

.stats-actions {
    text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .tab-nav {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        flex: none;
        min-width: 100px;
    }
    
    .card-display {
        font-size: 2rem;
        padding: 20px;
        min-height: 100px;
    }
    
    .count-display {
        flex-direction: column;
        gap: 20px;
    }
    
    .practice-stats {
        flex-direction: column;
        gap: 15px;
    }
}
