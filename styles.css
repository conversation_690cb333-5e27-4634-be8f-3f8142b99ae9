/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'SF Pro Display', system-ui, -apple-system, sans-serif;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    background-attachment: fixed;
    color: #1e293b;
    min-height: 100vh;
    font-weight: 400;
    line-height: 1.6;
    position: relative;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    position: relative;
    z-index: 1;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 48px;
    color: white;
    padding: 32px 0;
    position: relative;
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 4px;
    background: linear-gradient(90deg, transparent, #8b5cf6, #06b6d4, #8b5cf6, transparent);
    border-radius: 2px;
}

header h1 {
    font-size: 3.5rem;
    margin-bottom: 16px;
    font-weight: 800;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 25%, #e2e8f0 50%, #cbd5e1 75%, #94a3b8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    letter-spacing: -0.03em;
    position: relative;
}

header h1::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: linear-gradient(90deg, #8b5cf6, #06b6d4);
    border-radius: 1px;
}

header p {
    font-size: 1.4rem;
    opacity: 0.9;
    font-weight: 400;
    color: #e2e8f0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Navigation Tabs */
.tab-nav {
    display: flex;
    justify-content: center;
    margin-bottom: 48px;
    background: rgba(255,255,255,0.05);
    border-radius: 20px;
    padding: 8px;
    backdrop-filter: blur(40px);
    border: 1px solid rgba(255,255,255,0.15);
    box-shadow:
        0 20px 40px rgba(0,0,0,0.3),
        0 8px 16px rgba(0,0,0,0.2),
        inset 0 1px 0 rgba(255,255,255,0.1);
    position: relative;
}

.tab-nav::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(6, 182, 212, 0.3), rgba(139, 92, 246, 0.3));
    border-radius: 20px;
    z-index: -1;
}

.tab-btn {
    background: transparent;
    border: none;
    color: #e2e8f0;
    padding: 14px 28px;
    cursor: pointer;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex: 1;
    max-width: 160px;
    position: relative;
    overflow: hidden;
}

.tab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tab-btn:hover::before {
    opacity: 1;
}

.tab-btn:hover {
    color: white;
    transform: translateY(-1px);
}

.tab-btn.active {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
    color: white;
    font-weight: 700;
    box-shadow:
        0 8px 25px rgba(139, 92, 246, 0.4),
        0 4px 12px rgba(139, 92, 246, 0.3),
        inset 0 1px 0 rgba(255,255,255,0.2);
    transform: translateY(-2px);
    position: relative;
}

.tab-btn.active::before {
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 100%);
    opacity: 1;
}

/* Tab Content */
.tab-content {
    display: none;
    background: rgba(255,255,255,0.95);
    border-radius: 28px;
    padding: 48px;
    box-shadow:
        0 32px 80px rgba(0,0,0,0.25),
        0 16px 40px rgba(0,0,0,0.15),
        0 8px 20px rgba(0,0,0,0.1),
        inset 0 1px 0 rgba(255,255,255,0.8);
    min-height: 700px;
    border: 1px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(40px);
    position: relative;
}

.tab-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    border-radius: 28px;
    pointer-events: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.4s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Buttons */
.btn {
    padding: 14px 28px;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn.primary {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn.primary:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.btn.secondary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
}

.btn.secondary:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.btn.danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.btn.danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.btn:disabled {
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn:disabled::before {
    display: none;
}

/* Learn Tab Styles */
.strategy-explanation h2 {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 32px;
    font-size: 2.5rem;
    font-weight: 800;
    text-align: center;
    position: relative;
}

.strategy-explanation h2::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #8b5cf6, #06b6d4);
    border-radius: 2px;
}

.strategy-explanation h3 {
    color: #1e293b;
    margin: 32px 0 20px 0;
    font-size: 1.6rem;
    font-weight: 700;
    position: relative;
    padding-left: 20px;
}

.strategy-explanation h3::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #8b5cf6, #06b6d4);
    border-radius: 2px;
}

.card-values {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 28px;
    margin: 40px 0;
}

.value-group {
    padding: 32px 24px;
    border-radius: 20px;
    text-align: center;
    box-shadow:
        0 20px 40px rgba(0,0,0,0.1),
        0 8px 16px rgba(0,0,0,0.05),
        inset 0 1px 0 rgba(255,255,255,0.3);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255,255,255,0.2);
}

.value-group::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.value-group.low-cards {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 50%, #f59e0b 100%);
}

.value-group.neutral-cards {
    background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 50%, #0284c7 100%);
}

.value-group.high-cards {
    background: linear-gradient(135deg, #fce7f3 0%, #f9a8d4 50%, #ec4899 100%);
}

.value-group h4 {
    margin-bottom: 16px;
    font-size: 1.4rem;
    font-weight: 800;
    color: #1e293b;
    text-shadow: 0 1px 2px rgba(255,255,255,0.5);
}

.value-group .cards {
    font-size: 1.8rem;
    font-weight: 900;
    margin: 16px 0;
    color: #1e293b;
    text-shadow: 0 1px 2px rgba(255,255,255,0.3);
    letter-spacing: 2px;
}

.value-group p {
    font-weight: 600;
    color: #475569;
    font-size: 1rem;
    margin-top: 12px;
}

.strategy-tips ul {
    list-style: none;
    padding-left: 0;
}

.strategy-tips li {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    margin: 16px 0;
    padding: 24px;
    border-radius: 16px;
    border-left: 6px solid transparent;
    background-clip: padding-box;
    position: relative;
    box-shadow: 0 8px 20px rgba(0,0,0,0.08);
}

.strategy-tips li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6px;
    background: linear-gradient(135deg, #8b5cf6, #06b6d4);
    border-radius: 0 3px 3px 0;
}

.strategy-tips li strong {
    color: #8b5cf6;
    font-weight: 700;
}

/* Practice Tab Styles */
.practice-controls {
    display: flex;
    align-items: center;
    gap: 24px;
    margin-bottom: 40px;
    flex-wrap: wrap;
    padding: 24px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 16px;
    border: 1px solid #e2e8f0;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 12px;
}

.control-group label {
    font-weight: 600;
    color: #475569;
    font-size: 0.95rem;
}

.control-group select {
    padding: 10px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    background: white;
    color: #1e293b;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 140px;
}

.control-group select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.practice-timer {
    text-align: center;
    font-size: 1.8rem;
    font-weight: 700;
    color: #3b82f6;
    margin-bottom: 24px;
    padding: 16px;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border-radius: 12px;
    border: 2px solid #93c5fd;
}

.practice-area {
    text-align: center;
}

.current-card {
    margin-bottom: 30px;
}

.card-display {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border: 3px solid #3b82f6;
    border-radius: 20px;
    padding: 48px;
    font-size: 3.5rem;
    font-weight: 800;
    color: #1e293b;
    min-height: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 12px 40px rgba(0,0,0,0.12), 0 4px 16px rgba(0,0,0,0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 197, 253, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card-display:hover::before {
    opacity: 1;
}

.card-display.large {
    font-size: 4.5rem;
    min-height: 220px;
    padding: 56px;
}

.card-display.red {
    color: #dc2626;
    border-color: #dc2626;
}

.card-display.red::before {
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.05) 0%, rgba(248, 113, 113, 0.05) 100%);
}

.count-display {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 24px;
    margin: 40px 0;
    padding: 32px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 16px;
    border: 1px solid #e2e8f0;
}

.count-section {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.count-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.count-section label {
    display: block;
    font-weight: 700;
    margin-bottom: 12px;
    font-size: 1rem;
    color: #475569;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.count-section input {
    padding: 16px;
    font-size: 1.8rem;
    text-align: center;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    width: 120px;
    font-weight: 700;
    color: #1e293b;
    transition: all 0.3s ease;
}

.count-section input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.count-section span {
    font-size: 1.8rem;
    font-weight: 800;
    color: #3b82f6;
    display: block;
    padding: 16px;
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border-radius: 10px;
    border: 2px solid #93c5fd;
}

.practice-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 30px;
    flex-wrap: wrap;
}

.stat {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    min-width: 120px;
}

.stat-label {
    display: block;
    font-weight: 700;
    margin-bottom: 8px;
    color: #475569;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Practice Results */
.practice-results {
    margin-top: 40px;
    padding: 32px;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    border-radius: 16px;
    border: 2px solid #10b981;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.15);
}

.practice-results h3 {
    text-align: center;
    color: #047857;
    font-size: 1.8rem;
    font-weight: 800;
    margin-bottom: 24px;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    border: 1px solid #a7f3d0;
}

.result-label {
    font-weight: 600;
    color: #047857;
    font-size: 0.95rem;
}

.result-item span:last-child {
    font-weight: 800;
    font-size: 1.2rem;
    color: #065f46;
}

/* Drill Tab Styles */
.drill-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.drill-settings label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.timer {
    text-align: center;
    font-size: 2rem;
    font-weight: bold;
    color: #2a5298;
    margin-bottom: 20px;
}

.drill-input {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.drill-input input {
    padding: 12px;
    font-size: 1.2rem;
    text-align: center;
    border: 2px solid #ddd;
    border-radius: 8px;
    width: 120px;
}

.drill-results {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.result-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.result-stat {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    background: white;
    border-radius: 6px;
}

/* Game Tab Styles */
.game-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.game-controls select {
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
}

.game-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.info-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    min-width: 140px;
}

.info-item .label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
    color: #666;
}

.game-table {
    margin: 30px 0;
}

.dealer-area, .player-area {
    margin: 20px 0;
    text-align: center;
}

.dealer-area h3, .player-area h3 {
    margin-bottom: 15px;
    color: #2a5298;
}

.card-area {
    min-height: 100px;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.game-card {
    background: white;
    border: 2px solid #333;
    border-radius: 8px;
    padding: 15px 10px;
    font-size: 1.2rem;
    font-weight: bold;
    min-width: 50px;
    text-align: center;
}

.game-card.red {
    color: #d32f2f;
}

.game-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* Stats Tab Styles */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 28px;
    margin-bottom: 48px;
}

.stat-card {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
    color: white;
    padding: 32px 24px;
    border-radius: 20px;
    text-align: center;
    box-shadow:
        0 20px 40px rgba(139, 92, 246, 0.3),
        0 8px 16px rgba(139, 92, 246, 0.2),
        inset 0 1px 0 rgba(255,255,255,0.2);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow:
        0 32px 60px rgba(139, 92, 246, 0.4),
        0 16px 32px rgba(139, 92, 246, 0.3),
        inset 0 1px 0 rgba(255,255,255,0.3);
}

.stat-card h3 {
    margin-bottom: 20px;
    font-size: 1.2rem;
    opacity: 0.95;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-value {
    font-size: 3rem;
    font-weight: 900;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.progress-chart {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 32px;
    border-radius: 20px;
    margin-bottom: 40px;
    text-align: center;
    box-shadow:
        0 16px 32px rgba(0,0,0,0.08),
        0 8px 16px rgba(0,0,0,0.04),
        inset 0 1px 0 rgba(255,255,255,0.8);
    border: 1px solid rgba(255,255,255,0.3);
    position: relative;
}

.progress-chart::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.02) 0%, rgba(6, 182, 212, 0.02) 100%);
    border-radius: 20px;
    pointer-events: none;
}

.progress-chart h3 {
    margin-bottom: 28px;
    background: linear-gradient(135deg, #8b5cf6 0%, #06b6d4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.6rem;
    font-weight: 700;
}

.stats-actions {
    text-align: center;
}

/* Premium Animations */
@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-4px); }
}

.stat-card {
    animation: float 6s ease-in-out infinite;
}

.stat-card:nth-child(2) {
    animation-delay: -2s;
}

.stat-card:nth-child(3) {
    animation-delay: -4s;
}

.stat-card:nth-child(4) {
    animation-delay: -6s;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 20px;
    }

    .tab-content {
        padding: 32px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 16px;
    }

    header h1 {
        font-size: 2.5rem;
    }

    header p {
        font-size: 1.2rem;
    }

    .tab-nav {
        flex-wrap: wrap;
        gap: 8px;
    }

    .tab-btn {
        flex: none;
        min-width: 120px;
        padding: 12px 20px;
    }

    .tab-content {
        padding: 24px;
        min-height: 500px;
    }

    .card-display {
        font-size: 2.5rem;
        padding: 32px 20px;
        min-height: 140px;
    }

    .count-display {
        grid-template-columns: 1fr;
        gap: 16px;
        padding: 24px;
    }

    .practice-stats {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 16px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .practice-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }

    .control-group {
        justify-content: space-between;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 2rem;
    }

    .tab-content {
        padding: 20px;
    }

    .card-display {
        font-size: 2rem;
        padding: 24px 16px;
        min-height: 120px;
    }

    .count-display {
        padding: 20px;
    }

    .count-section input {
        width: 100px;
        font-size: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}
