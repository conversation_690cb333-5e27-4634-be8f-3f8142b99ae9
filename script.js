// Hi-Lo Card Counting Trainer
class CardCountingTrainer {
    constructor() {
        this.cards = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
        this.suits = ['♠', '♥', '♦', '♣'];
        this.hiLoValues = {
            '2': 1, '3': 1, '4': 1, '5': 1, '6': 1,
            '7': 0, '8': 0, '9': 0,
            '10': -1, 'J': -1, 'Q': -1, 'K': -1, 'A': -1
        };
        
        this.runningCount = 0;
        this.cardsDealt = 0;
        this.practiceActive = false;
        this.drillActive = false;
        this.gameActive = false;
        this.practiceTimer = null;
        this.practiceTimeRemaining = 0;
        this.practiceDecks = 6;
        this.practiceCorrect = 0;
        this.dealerCards = [];
        this.playerCards = [];
        this.insuranceTaken = false;
        this.handSurrendered = false;
        
        // Statistics
        this.stats = this.loadStats();
        
        this.initializeApp();
    }

    initializeApp() {
        this.setupTabNavigation();
        this.setupPracticeMode();
        this.setupSpeedDrill();
        this.setupGameSimulation();
        this.setupStatistics();
        this.updateStatsDisplay();
    }

    setupTabNavigation() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetTab = btn.dataset.tab;
                
                // Remove active class from all tabs and contents
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                btn.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });
    }

    setupPracticeMode() {
        const startBtn = document.getElementById('start-practice');
        const resetBtn = document.getElementById('reset-practice');
        const userCountInput = document.getElementById('user-count');
        const speedSelect = document.getElementById('practice-speed');

        startBtn.addEventListener('click', () => {
            if (!this.practiceActive) {
                this.startPractice();
            } else {
                this.stopPractice();
            }
        });

        resetBtn.addEventListener('click', () => {
            this.resetPractice();
        });

        userCountInput.addEventListener('input', () => {
            this.checkPracticeAccuracy();
        });
    }

    setupSpeedDrill() {
        const startBtn = document.getElementById('start-drill');
        const submitBtn = document.getElementById('submit-drill');
        const drillCountInput = document.getElementById('drill-count');

        startBtn.addEventListener('click', () => {
            this.startSpeedDrill();
        });

        submitBtn.addEventListener('click', () => {
            this.submitDrillResult();
        });

        drillCountInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.submitDrillResult();
            }
        });
    }

    setupGameSimulation() {
        const startBtn = document.getElementById('start-game');
        const dealBtn = document.getElementById('deal-hand');
        const hitBtn = document.getElementById('hit-card');
        const standBtn = document.getElementById('stand');
        const surrenderBtn = document.getElementById('surrender');
        const insuranceBtn = document.getElementById('insurance');
        const noInsuranceBtn = document.getElementById('no-insurance');
        const nextBtn = document.getElementById('next-hand');

        startBtn.addEventListener('click', () => {
            this.startGameSimulation();
        });

        dealBtn.addEventListener('click', () => {
            this.dealHand();
        });

        hitBtn.addEventListener('click', () => {
            this.hitCard();
        });

        standBtn.addEventListener('click', () => {
            this.stand();
        });

        surrenderBtn.addEventListener('click', () => {
            this.surrender();
        });

        insuranceBtn.addEventListener('click', () => {
            this.takeInsurance();
        });

        noInsuranceBtn.addEventListener('click', () => {
            this.declineInsurance();
        });

        nextBtn.addEventListener('click', () => {
            this.nextHand();
        });
    }

    setupStatistics() {
        const resetBtn = document.getElementById('reset-stats');
        resetBtn.addEventListener('click', () => {
            if (confirm('Are you sure you want to reset all statistics?')) {
                this.resetStats();
            }
        });
    }

    // Practice Mode Functions
    startPractice() {
        this.practiceActive = true;
        this.runningCount = 0;
        this.cardsDealt = 0;
        this.practiceCorrect = 0;
        this.practiceDecks = parseInt(document.getElementById('practice-decks').value);

        // Setup timer
        const duration = parseInt(document.getElementById('practice-duration').value);
        if (duration > 0) {
            this.practiceTimeRemaining = duration;
            document.getElementById('practice-timer').style.display = 'block';
            this.updatePracticeTimer();
        } else {
            document.getElementById('practice-timer').style.display = 'none';
        }

        document.getElementById('start-practice').textContent = 'Stop Practice';
        document.getElementById('actual-count').textContent = '0';
        document.getElementById('true-count').textContent = '0.0';
        document.getElementById('user-count').value = '';
        document.getElementById('cards-shown').textContent = '0';
        document.getElementById('accuracy').textContent = '100%';
        document.getElementById('decks-remaining').textContent = this.practiceDecks.toFixed(1);
        document.getElementById('practice-results').style.display = 'none';

        this.showNextPracticeCard();
    }

    updatePracticeTimer() {
        if (!this.practiceActive || this.practiceTimeRemaining <= 0) {
            if (this.practiceActive) {
                this.endPracticeSession();
            }
            return;
        }

        document.getElementById('timer-display').textContent = this.practiceTimeRemaining;
        this.practiceTimeRemaining--;

        this.practiceTimer = setTimeout(() => this.updatePracticeTimer(), 1000);
    }

    endPracticeSession() {
        this.practiceActive = false;
        if (this.practiceTimer) {
            clearTimeout(this.practiceTimer);
            this.practiceTimer = null;
        }

        document.getElementById('start-practice').textContent = 'Start Practice';
        document.getElementById('practice-card').textContent = 'Session Complete!';
        document.getElementById('practice-timer').style.display = 'none';

        // Show results
        this.showPracticeResults();

        // Update statistics
        this.stats.totalCards += this.cardsDealt;
        this.stats.totalSessions += 1;
        this.saveStats();
        this.updateStatsDisplay();
    }

    showPracticeResults() {
        const finalAccuracy = this.cardsDealt > 0 ? Math.round((this.practiceCorrect / this.cardsDealt) * 100) : 100;
        const decksRemaining = Math.max(0.1, this.practiceDecks - (this.cardsDealt / 52));
        const trueCount = this.runningCount / decksRemaining;

        document.getElementById('final-running-count').textContent = this.runningCount;
        document.getElementById('final-true-count').textContent = trueCount.toFixed(1);
        document.getElementById('final-cards-count').textContent = this.cardsDealt;
        document.getElementById('final-accuracy').textContent = finalAccuracy + '%';
        document.getElementById('practice-results').style.display = 'block';
    }

    stopPractice() {
        this.endPracticeSession();
    }

    resetPractice() {
        this.practiceActive = false;
        if (this.practiceTimer) {
            clearTimeout(this.practiceTimer);
            this.practiceTimer = null;
        }

        this.runningCount = 0;
        this.cardsDealt = 0;
        this.practiceCorrect = 0;
        this.practiceDecks = parseInt(document.getElementById('practice-decks').value);

        document.getElementById('start-practice').textContent = 'Start Practice';
        document.getElementById('practice-card').textContent = 'Click Start to Begin';
        document.getElementById('actual-count').textContent = '0';
        document.getElementById('true-count').textContent = '0.0';
        document.getElementById('user-count').value = '';
        document.getElementById('cards-shown').textContent = '0';
        document.getElementById('accuracy').textContent = '100%';
        document.getElementById('decks-remaining').textContent = this.practiceDecks.toFixed(1);
        document.getElementById('practice-timer').style.display = 'none';
        document.getElementById('practice-results').style.display = 'none';
    }

    showNextPracticeCard() {
        if (!this.practiceActive) return;

        const card = this.getRandomCard();
        const cardDisplay = document.getElementById('practice-card');

        cardDisplay.textContent = card.display;
        cardDisplay.className = 'card-display' + (card.isRed ? ' red' : '');

        this.runningCount += this.hiLoValues[card.value];
        this.cardsDealt++;

        // Calculate decks remaining and true count
        const decksRemaining = Math.max(0.1, this.practiceDecks - (this.cardsDealt / 52));
        const trueCount = this.runningCount / decksRemaining;

        document.getElementById('actual-count').textContent = this.runningCount;
        document.getElementById('true-count').textContent = trueCount.toFixed(1);
        document.getElementById('cards-shown').textContent = this.cardsDealt;
        document.getElementById('decks-remaining').textContent = decksRemaining.toFixed(1);

        const speed = parseInt(document.getElementById('practice-speed').value);
        setTimeout(() => this.showNextPracticeCard(), speed);
    }

    checkPracticeAccuracy() {
        const userCount = parseInt(document.getElementById('user-count').value) || 0;
        const actualCount = this.runningCount;

        if (this.cardsDealt > 0) {
            // Count as correct if user is within 1 of the actual count (to account for timing)
            const isCorrect = Math.abs(userCount - actualCount) <= 1;
            if (isCorrect && userCount !== 0) {
                this.practiceCorrect = Math.max(this.practiceCorrect, this.cardsDealt - Math.abs(userCount - actualCount));
            }

            const accuracy = Math.round((this.practiceCorrect / this.cardsDealt) * 100);
            document.getElementById('accuracy').textContent = accuracy + '%';
        }
    }

    // Speed Drill Functions
    startSpeedDrill() {
        this.drillActive = true;
        this.drillCount = 0;
        this.drillCards = [];
        this.drillTimer = 30;
        
        document.getElementById('start-drill').disabled = true;
        document.getElementById('drill-count').value = '';
        document.getElementById('submit-drill').disabled = true;
        document.getElementById('drill-results').style.display = 'none';
        
        this.updateDrillTimer();
        this.showDrillCards();
    }

    updateDrillTimer() {
        document.getElementById('drill-timer').textContent = this.drillTimer;
        
        if (this.drillTimer > 0 && this.drillActive) {
            this.drillTimer--;
            setTimeout(() => this.updateDrillTimer(), 1000);
        } else if (this.drillActive) {
            this.endSpeedDrill();
        }
    }

    showDrillCards() {
        if (!this.drillActive || this.drillTimer <= 0) return;

        const card = this.getRandomCard();
        const cardDisplay = document.getElementById('drill-card');
        const showValues = document.getElementById('show-values').checked;
        
        if (showValues) {
            cardDisplay.textContent = `${card.display} (${this.hiLoValues[card.value] > 0 ? '+' : ''}${this.hiLoValues[card.value]})`;
        } else {
            cardDisplay.textContent = card.display;
        }
        
        cardDisplay.className = 'card-display large' + (card.isRed ? ' red' : '');
        
        this.drillCount += this.hiLoValues[card.value];
        this.drillCards.push(card);
        
        setTimeout(() => this.showDrillCards(), 800);
    }

    endSpeedDrill() {
        this.drillActive = false;
        document.getElementById('drill-card').textContent = 'Time\'s Up!';
        document.getElementById('submit-drill').disabled = false;
        document.getElementById('start-drill').disabled = false;
    }

    submitDrillResult() {
        const userCount = parseInt(document.getElementById('drill-count').value) || 0;
        const correctCount = this.drillCount;
        const cardsProcessed = this.drillCards.length;
        const accuracy = userCount === correctCount ? 100 : 0;
        
        document.getElementById('drill-cards-count').textContent = cardsProcessed;
        document.getElementById('drill-user-count').textContent = userCount;
        document.getElementById('drill-correct-count').textContent = correctCount;
        document.getElementById('drill-accuracy').textContent = accuracy + '%';
        document.getElementById('drill-results').style.display = 'block';
        
        // Update statistics
        const speed = Math.round((cardsProcessed / 30) * 60); // cards per minute
        if (speed > this.stats.bestSpeed) {
            this.stats.bestSpeed = speed;
        }
        this.stats.totalCards += cardsProcessed;
        this.saveStats();
        this.updateStatsDisplay();
    }

    // Game Simulation Functions
    startGameSimulation() {
        this.gameActive = true;
        this.gameRunningCount = 0;
        this.gameDecks = parseInt(document.getElementById('deck-count').value);
        this.gameCardsRemaining = this.gameDecks * 52;
        this.dealerCards = [];
        this.playerCards = [];
        this.insuranceTaken = false;
        this.handSurrendered = false;

        document.getElementById('deal-hand').disabled = false;
        document.getElementById('game-running-count').textContent = '0';
        document.getElementById('game-true-count').textContent = '0';
        document.getElementById('game-decks-remaining').textContent = this.gameDecks;
        document.getElementById('game-messages').style.display = 'none';

        this.clearGameTable();
        this.resetGameButtons();
    }

    resetGameButtons() {
        document.getElementById('hit-card').disabled = true;
        document.getElementById('stand').disabled = true;
        document.getElementById('surrender').disabled = true;
        document.getElementById('insurance').style.display = 'none';
        document.getElementById('no-insurance').style.display = 'none';
        document.getElementById('next-hand').disabled = true;
    }

    dealHand() {
        this.clearGameTable();
        this.dealerCards = [];
        this.playerCards = [];
        this.insuranceTaken = false;
        this.handSurrendered = false;
        document.getElementById('game-messages').style.display = 'none';

        // Deal 2 cards to player, 2 to dealer (1 face down)
        const playerCard1 = this.dealCardTo('player');
        const dealerCard1 = this.dealCardTo('dealer');
        const playerCard2 = this.dealCardTo('player');
        const dealerCard2 = this.dealCardTo('dealer', true); // face down

        // Check for insurance if dealer shows Ace
        if (dealerCard1.value === 'A') {
            this.offerInsurance();
        } else {
            this.enablePlayerActions();
        }

        document.getElementById('deal-hand').disabled = true;
    }

    offerInsurance() {
        document.getElementById('insurance').style.display = 'inline-block';
        document.getElementById('no-insurance').style.display = 'inline-block';
        document.getElementById('insurance').disabled = false;
        document.getElementById('no-insurance').disabled = false;

        this.showGameMessage('Insurance Offered', 'Dealer shows an Ace. Would you like insurance?');
    }

    takeInsurance() {
        this.insuranceTaken = true;
        document.getElementById('insurance').style.display = 'none';
        document.getElementById('no-insurance').style.display = 'none';

        this.showGameMessage('Insurance Taken', 'You have taken insurance for half your bet.');

        // Check for dealer blackjack
        setTimeout(() => {
            this.checkDealerBlackjack();
        }, 2000);
    }

    declineInsurance() {
        this.insuranceTaken = false;
        document.getElementById('insurance').style.display = 'none';
        document.getElementById('no-insurance').style.display = 'none';

        // Check for dealer blackjack
        this.checkDealerBlackjack();
    }

    checkDealerBlackjack() {
        const dealerValue = this.getHandValue(this.dealerCards);

        if (dealerValue === 21) {
            // Dealer has blackjack
            this.revealDealerCard();

            if (this.insuranceTaken) {
                this.showGameMessage('Dealer Blackjack!', 'Dealer has blackjack. Your insurance bet pays 2:1.');
            } else {
                this.showGameMessage('Dealer Blackjack!', 'Dealer has blackjack. You lose this hand.');
            }

            document.getElementById('next-hand').disabled = false;
        } else {
            // No dealer blackjack
            if (this.insuranceTaken) {
                this.showGameMessage('No Blackjack', 'Dealer does not have blackjack. Insurance bet is lost.');
                setTimeout(() => {
                    document.getElementById('game-messages').style.display = 'none';
                    this.enablePlayerActions();
                }, 2000);
            } else {
                this.enablePlayerActions();
            }
        }
    }

    enablePlayerActions() {
        document.getElementById('hit-card').disabled = false;
        document.getElementById('stand').disabled = false;
        document.getElementById('surrender').disabled = false;
    }

    dealCardTo(target, faceDown = false) {
        const card = this.getRandomCard();
        const cardElement = document.createElement('div');
        cardElement.className = 'game-card' + (card.isRed ? ' red' : '');
        cardElement.textContent = faceDown ? '?' : card.display;

        document.getElementById(target + '-cards').appendChild(cardElement);

        // Store card in appropriate array
        if (target === 'dealer') {
            this.dealerCards.push({...card, faceDown: faceDown});
        } else {
            this.playerCards.push(card);
        }

        if (!faceDown) {
            this.gameRunningCount += this.hiLoValues[card.value];
            this.updateGameCounts();
        }

        this.gameCardsRemaining--;
        return card;
    }

    hitCard() {
        this.dealCardTo('player');

        // Disable surrender after hitting
        document.getElementById('surrender').disabled = true;

        // Check for bust
        const playerValue = this.getHandValue(this.playerCards);
        if (playerValue > 21) {
            this.showGameMessage('Player Bust!', `You busted with ${playerValue}. You lose this hand.`);
            this.disablePlayerActions();
            document.getElementById('next-hand').disabled = false;
        }
    }

    stand() {
        this.disablePlayerActions();
        this.revealDealerCard();
        this.dealerPlay();
    }

    surrender() {
        this.handSurrendered = true;
        this.showGameMessage('Hand Surrendered', 'You surrendered this hand and lose half your bet.');
        this.disablePlayerActions();
        document.getElementById('next-hand').disabled = false;
    }

    revealDealerCard() {
        // Reveal dealer's face-down card
        const dealerCards = document.getElementById('dealer-cards');
        const faceDownCard = dealerCards.querySelector('.game-card:last-child');

        if (faceDownCard && faceDownCard.textContent === '?') {
            const hiddenCard = this.dealerCards.find(card => card.faceDown);
            if (hiddenCard) {
                faceDownCard.textContent = hiddenCard.display;
                faceDownCard.className = 'game-card' + (hiddenCard.isRed ? ' red' : '');
                hiddenCard.faceDown = false;

                this.gameRunningCount += this.hiLoValues[hiddenCard.value];
                this.updateGameCounts();
            }
        }
    }

    dealerPlay() {
        let dealerValue = this.getHandValue(this.dealerCards);

        // Dealer hits on soft 17
        while (dealerValue < 17 || (dealerValue === 17 && this.isSoftHand(this.dealerCards))) {
            this.dealCardTo('dealer');
            dealerValue = this.getHandValue(this.dealerCards);
        }

        // Determine winner
        const playerValue = this.getHandValue(this.playerCards);

        if (dealerValue > 21) {
            this.showGameMessage('Dealer Bust!', `Dealer busted with ${dealerValue}. You win!`);
        } else if (playerValue > dealerValue) {
            this.showGameMessage('You Win!', `Your ${playerValue} beats dealer's ${dealerValue}.`);
        } else if (dealerValue > playerValue) {
            this.showGameMessage('Dealer Wins', `Dealer's ${dealerValue} beats your ${playerValue}.`);
        } else {
            this.showGameMessage('Push', `Both have ${playerValue}. It's a tie.`);
        }

        document.getElementById('next-hand').disabled = false;
    }

    disablePlayerActions() {
        document.getElementById('hit-card').disabled = true;
        document.getElementById('stand').disabled = true;
        document.getElementById('surrender').disabled = true;
    }

    nextHand() {
        document.getElementById('next-hand').disabled = true;
        document.getElementById('deal-hand').disabled = false;
        document.getElementById('game-messages').style.display = 'none';
        this.resetGameButtons();
    }

    showGameMessage(title, text) {
        document.getElementById('message-title').textContent = title;
        document.getElementById('message-text').textContent = text;
        document.getElementById('game-messages').style.display = 'block';
    }

    getHandValue(cards) {
        let value = 0;
        let aces = 0;

        for (const card of cards) {
            if (card.faceDown) continue;

            if (card.value === 'A') {
                aces++;
                value += 11;
            } else if (['K', 'Q', 'J'].includes(card.value)) {
                value += 10;
            } else {
                value += parseInt(card.value);
            }
        }

        // Adjust for aces
        while (value > 21 && aces > 0) {
            value -= 10;
            aces--;
        }

        return value;
    }

    isSoftHand(cards) {
        let value = 0;
        let aces = 0;

        for (const card of cards) {
            if (card.faceDown) continue;

            if (card.value === 'A') {
                aces++;
                value += 11;
            } else if (['K', 'Q', 'J'].includes(card.value)) {
                value += 10;
            } else {
                value += parseInt(card.value);
            }
        }

        // Check if we have an ace counting as 11
        return aces > 0 && value <= 21;
    }

    clearGameTable() {
        document.getElementById('dealer-cards').innerHTML = '';
        document.getElementById('player-cards').innerHTML = '';
    }

    updateGameCounts() {
        const decksRemaining = Math.max(1, Math.round(this.gameCardsRemaining / 52 * 10) / 10);
        const trueCount = Math.round(this.gameRunningCount / decksRemaining * 10) / 10;
        
        document.getElementById('game-running-count').textContent = this.gameRunningCount;
        document.getElementById('game-true-count').textContent = trueCount;
        document.getElementById('game-decks-remaining').textContent = decksRemaining;
    }

    // Utility Functions
    getRandomCard() {
        const value = this.cards[Math.floor(Math.random() * this.cards.length)];
        const suit = this.suits[Math.floor(Math.random() * this.suits.length)];
        const isRed = suit === '♥' || suit === '♦';
        
        return {
            value: value,
            suit: suit,
            display: value + suit,
            isRed: isRed
        };
    }

    // Statistics Functions
    loadStats() {
        const defaultStats = {
            totalCards: 0,
            totalSessions: 0,
            bestSpeed: 0,
            accuracyHistory: []
        };
        
        const saved = localStorage.getItem('hiLoStats');
        return saved ? JSON.parse(saved) : defaultStats;
    }

    saveStats() {
        localStorage.setItem('hiLoStats', JSON.stringify(this.stats));
    }

    updateStatsDisplay() {
        document.getElementById('total-cards').textContent = this.stats.totalCards;
        document.getElementById('total-sessions').textContent = this.stats.totalSessions;
        document.getElementById('best-speed').textContent = this.stats.bestSpeed + ' cards/min';
        
        // Calculate overall accuracy (simplified)
        const accuracy = this.stats.totalCards > 0 ? 85 : 0; // Placeholder calculation
        document.getElementById('overall-accuracy').textContent = accuracy + '%';
    }

    resetStats() {
        this.stats = {
            totalCards: 0,
            totalSessions: 0,
            bestSpeed: 0,
            accuracyHistory: []
        };
        this.saveStats();
        this.updateStatsDisplay();
    }
}

// Initialize the app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new CardCountingTrainer();
});
