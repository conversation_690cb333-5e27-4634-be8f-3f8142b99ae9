// Hi-Lo Card Counting Trainer
class CardCountingTrainer {
    constructor() {
        this.cards = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
        this.suits = ['♠', '♥', '♦', '♣'];
        this.hiLoValues = {
            '2': 1, '3': 1, '4': 1, '5': 1, '6': 1,
            '7': 0, '8': 0, '9': 0,
            '10': -1, 'J': -1, 'Q': -1, 'K': -1, 'A': -1
        };
        
        this.runningCount = 0;
        this.cardsDealt = 0;
        this.practiceActive = false;
        this.drillActive = false;
        this.gameActive = false;
        
        // Statistics
        this.stats = this.loadStats();
        
        this.initializeApp();
    }

    initializeApp() {
        this.setupTabNavigation();
        this.setupPracticeMode();
        this.setupSpeedDrill();
        this.setupGameSimulation();
        this.setupStatistics();
        this.updateStatsDisplay();
    }

    setupTabNavigation() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetTab = btn.dataset.tab;
                
                // Remove active class from all tabs and contents
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab and corresponding content
                btn.classList.add('active');
                document.getElementById(targetTab).classList.add('active');
            });
        });
    }

    setupPracticeMode() {
        const startBtn = document.getElementById('start-practice');
        const resetBtn = document.getElementById('reset-practice');
        const userCountInput = document.getElementById('user-count');
        const speedSelect = document.getElementById('practice-speed');

        startBtn.addEventListener('click', () => {
            if (!this.practiceActive) {
                this.startPractice();
            } else {
                this.stopPractice();
            }
        });

        resetBtn.addEventListener('click', () => {
            this.resetPractice();
        });

        userCountInput.addEventListener('input', () => {
            this.checkPracticeAccuracy();
        });
    }

    setupSpeedDrill() {
        const startBtn = document.getElementById('start-drill');
        const submitBtn = document.getElementById('submit-drill');
        const drillCountInput = document.getElementById('drill-count');

        startBtn.addEventListener('click', () => {
            this.startSpeedDrill();
        });

        submitBtn.addEventListener('click', () => {
            this.submitDrillResult();
        });

        drillCountInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.submitDrillResult();
            }
        });
    }

    setupGameSimulation() {
        const startBtn = document.getElementById('start-game');
        const dealBtn = document.getElementById('deal-hand');
        const hitBtn = document.getElementById('hit-card');
        const standBtn = document.getElementById('stand');
        const nextBtn = document.getElementById('next-hand');

        startBtn.addEventListener('click', () => {
            this.startGameSimulation();
        });

        dealBtn.addEventListener('click', () => {
            this.dealHand();
        });

        hitBtn.addEventListener('click', () => {
            this.hitCard();
        });

        standBtn.addEventListener('click', () => {
            this.stand();
        });

        nextBtn.addEventListener('click', () => {
            this.nextHand();
        });
    }

    setupStatistics() {
        const resetBtn = document.getElementById('reset-stats');
        resetBtn.addEventListener('click', () => {
            if (confirm('Are you sure you want to reset all statistics?')) {
                this.resetStats();
            }
        });
    }

    // Practice Mode Functions
    startPractice() {
        this.practiceActive = true;
        this.runningCount = 0;
        this.cardsDealt = 0;
        this.practiceCorrect = 0;
        
        document.getElementById('start-practice').textContent = 'Stop Practice';
        document.getElementById('actual-count').textContent = '0';
        document.getElementById('user-count').value = '';
        document.getElementById('cards-shown').textContent = '0';
        document.getElementById('accuracy').textContent = '100%';
        
        this.showNextPracticeCard();
    }

    stopPractice() {
        this.practiceActive = false;
        document.getElementById('start-practice').textContent = 'Start Practice';
        document.getElementById('practice-card').textContent = 'Practice Stopped';
        
        // Update statistics
        this.stats.totalCards += this.cardsDealt;
        this.stats.totalSessions += 1;
        this.saveStats();
        this.updateStatsDisplay();
    }

    resetPractice() {
        this.stopPractice();
        this.runningCount = 0;
        this.cardsDealt = 0;
        document.getElementById('practice-card').textContent = 'Click Start to Begin';
        document.getElementById('actual-count').textContent = '0';
        document.getElementById('user-count').value = '';
        document.getElementById('cards-shown').textContent = '0';
        document.getElementById('accuracy').textContent = '100%';
    }

    showNextPracticeCard() {
        if (!this.practiceActive) return;

        const card = this.getRandomCard();
        const cardDisplay = document.getElementById('practice-card');
        
        cardDisplay.textContent = card.display;
        cardDisplay.className = 'card-display' + (card.isRed ? ' red' : '');
        
        this.runningCount += this.hiLoValues[card.value];
        this.cardsDealt++;
        
        document.getElementById('actual-count').textContent = this.runningCount;
        document.getElementById('cards-shown').textContent = this.cardsDealt;
        
        const speed = parseInt(document.getElementById('practice-speed').value);
        setTimeout(() => this.showNextPracticeCard(), speed);
    }

    checkPracticeAccuracy() {
        const userCount = parseInt(document.getElementById('user-count').value) || 0;
        const actualCount = this.runningCount;
        
        if (this.cardsDealt > 0) {
            const isCorrect = userCount === actualCount;
            if (isCorrect) this.practiceCorrect++;
            
            const accuracy = Math.round((this.practiceCorrect / this.cardsDealt) * 100);
            document.getElementById('accuracy').textContent = accuracy + '%';
        }
    }

    // Speed Drill Functions
    startSpeedDrill() {
        this.drillActive = true;
        this.drillCount = 0;
        this.drillCards = [];
        this.drillTimer = 30;
        
        document.getElementById('start-drill').disabled = true;
        document.getElementById('drill-count').value = '';
        document.getElementById('submit-drill').disabled = true;
        document.getElementById('drill-results').style.display = 'none';
        
        this.updateDrillTimer();
        this.showDrillCards();
    }

    updateDrillTimer() {
        document.getElementById('drill-timer').textContent = this.drillTimer;
        
        if (this.drillTimer > 0 && this.drillActive) {
            this.drillTimer--;
            setTimeout(() => this.updateDrillTimer(), 1000);
        } else if (this.drillActive) {
            this.endSpeedDrill();
        }
    }

    showDrillCards() {
        if (!this.drillActive || this.drillTimer <= 0) return;

        const card = this.getRandomCard();
        const cardDisplay = document.getElementById('drill-card');
        const showValues = document.getElementById('show-values').checked;
        
        if (showValues) {
            cardDisplay.textContent = `${card.display} (${this.hiLoValues[card.value] > 0 ? '+' : ''}${this.hiLoValues[card.value]})`;
        } else {
            cardDisplay.textContent = card.display;
        }
        
        cardDisplay.className = 'card-display large' + (card.isRed ? ' red' : '');
        
        this.drillCount += this.hiLoValues[card.value];
        this.drillCards.push(card);
        
        setTimeout(() => this.showDrillCards(), 800);
    }

    endSpeedDrill() {
        this.drillActive = false;
        document.getElementById('drill-card').textContent = 'Time\'s Up!';
        document.getElementById('submit-drill').disabled = false;
        document.getElementById('start-drill').disabled = false;
    }

    submitDrillResult() {
        const userCount = parseInt(document.getElementById('drill-count').value) || 0;
        const correctCount = this.drillCount;
        const cardsProcessed = this.drillCards.length;
        const accuracy = userCount === correctCount ? 100 : 0;
        
        document.getElementById('drill-cards-count').textContent = cardsProcessed;
        document.getElementById('drill-user-count').textContent = userCount;
        document.getElementById('drill-correct-count').textContent = correctCount;
        document.getElementById('drill-accuracy').textContent = accuracy + '%';
        document.getElementById('drill-results').style.display = 'block';
        
        // Update statistics
        const speed = Math.round((cardsProcessed / 30) * 60); // cards per minute
        if (speed > this.stats.bestSpeed) {
            this.stats.bestSpeed = speed;
        }
        this.stats.totalCards += cardsProcessed;
        this.saveStats();
        this.updateStatsDisplay();
    }

    // Game Simulation Functions
    startGameSimulation() {
        this.gameActive = true;
        this.gameRunningCount = 0;
        this.gameDecks = parseInt(document.getElementById('deck-count').value);
        this.gameCardsRemaining = this.gameDecks * 52;
        
        document.getElementById('deal-hand').disabled = false;
        document.getElementById('game-running-count').textContent = '0';
        document.getElementById('game-true-count').textContent = '0';
        document.getElementById('game-decks-remaining').textContent = this.gameDecks;
        
        this.clearGameTable();
    }

    dealHand() {
        this.clearGameTable();
        
        // Deal 2 cards to player, 2 to dealer (1 face down)
        this.dealCardTo('player');
        this.dealCardTo('dealer');
        this.dealCardTo('player');
        this.dealCardTo('dealer', true); // face down
        
        document.getElementById('deal-hand').disabled = true;
        document.getElementById('hit-card').disabled = false;
        document.getElementById('stand').disabled = false;
    }

    dealCardTo(target, faceDown = false) {
        const card = this.getRandomCard();
        const cardElement = document.createElement('div');
        cardElement.className = 'game-card' + (card.isRed ? ' red' : '');
        cardElement.textContent = faceDown ? '?' : card.display;
        
        document.getElementById(target + '-cards').appendChild(cardElement);
        
        if (!faceDown) {
            this.gameRunningCount += this.hiLoValues[card.value];
            this.updateGameCounts();
        }
        
        this.gameCardsRemaining--;
        return card;
    }

    hitCard() {
        this.dealCardTo('player');
        // In a real game, you'd check for bust here
    }

    stand() {
        // Reveal dealer's face-down card
        const dealerCards = document.getElementById('dealer-cards');
        const faceDownCard = dealerCards.querySelector('.game-card:last-child');
        const card = this.getRandomCard();
        faceDownCard.textContent = card.display;
        faceDownCard.className = 'game-card' + (card.isRed ? ' red' : '');
        
        this.gameRunningCount += this.hiLoValues[card.value];
        this.updateGameCounts();
        
        // Dealer hits on soft 17
        while (Math.random() < 0.5) { // Simplified dealer logic
            this.dealCardTo('dealer');
        }
        
        document.getElementById('hit-card').disabled = true;
        document.getElementById('stand').disabled = true;
        document.getElementById('next-hand').disabled = false;
    }

    nextHand() {
        document.getElementById('next-hand').disabled = true;
        document.getElementById('deal-hand').disabled = false;
    }

    clearGameTable() {
        document.getElementById('dealer-cards').innerHTML = '';
        document.getElementById('player-cards').innerHTML = '';
    }

    updateGameCounts() {
        const decksRemaining = Math.max(1, Math.round(this.gameCardsRemaining / 52 * 10) / 10);
        const trueCount = Math.round(this.gameRunningCount / decksRemaining * 10) / 10;
        
        document.getElementById('game-running-count').textContent = this.gameRunningCount;
        document.getElementById('game-true-count').textContent = trueCount;
        document.getElementById('game-decks-remaining').textContent = decksRemaining;
    }

    // Utility Functions
    getRandomCard() {
        const value = this.cards[Math.floor(Math.random() * this.cards.length)];
        const suit = this.suits[Math.floor(Math.random() * this.suits.length)];
        const isRed = suit === '♥' || suit === '♦';
        
        return {
            value: value,
            suit: suit,
            display: value + suit,
            isRed: isRed
        };
    }

    // Statistics Functions
    loadStats() {
        const defaultStats = {
            totalCards: 0,
            totalSessions: 0,
            bestSpeed: 0,
            accuracyHistory: []
        };
        
        const saved = localStorage.getItem('hiLoStats');
        return saved ? JSON.parse(saved) : defaultStats;
    }

    saveStats() {
        localStorage.setItem('hiLoStats', JSON.stringify(this.stats));
    }

    updateStatsDisplay() {
        document.getElementById('total-cards').textContent = this.stats.totalCards;
        document.getElementById('total-sessions').textContent = this.stats.totalSessions;
        document.getElementById('best-speed').textContent = this.stats.bestSpeed + ' cards/min';
        
        // Calculate overall accuracy (simplified)
        const accuracy = this.stats.totalCards > 0 ? 85 : 0; // Placeholder calculation
        document.getElementById('overall-accuracy').textContent = accuracy + '%';
    }

    resetStats() {
        this.stats = {
            totalCards: 0,
            totalSessions: 0,
            bestSpeed: 0,
            accuracyHistory: []
        };
        this.saveStats();
        this.updateStatsDisplay();
    }
}

// Initialize the app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new CardCountingTrainer();
});
